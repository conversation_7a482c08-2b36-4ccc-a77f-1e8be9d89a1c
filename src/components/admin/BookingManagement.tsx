import React, { useState } from 'react';
import { Check, AlertCircle, ChevronLeft, ChevronRight, Download, SortDesc, X, DollarSign, CreditCard } from 'lucide-react';
import { Booking, Product, Payment, SystemSettings } from '../../types';
import BookingList from './booking/BookingList';
import BookingFilters from './booking/BookingFilters';
import BookingStats from './booking/BookingStats';
import { updateBooking } from '../../services/database';

// Add sort options type for better type safety
type SortOrder = 'newest' | 'activity';

interface BookingManagementProps {
  bookings: Booking[];
  products: Product[];
  systemSettings: SystemSettings;
  onBookingsUpdate: (bookings: Booking[]) => void;
}

const BookingManagement: React.FC<BookingManagementProps> = ({
  bookings,
  products,
  systemSettings,
  onBookingsUpdate
}) => {
  const [expandedBookingId, setExpandedBookingId] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [isExporting, setIsExporting] = useState(false);
  // Add sort order state with 'newest' as default
  const [sortOrder, setSortOrder] = useState<SortOrder>('newest');
  // Add payment modal state
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentBookingId, setPaymentBookingId] = useState<string | null>(null);
  const [newPayment, setNewPayment] = useState<{
    amount: string;
    method: 'cash' | 'card' | 'bank_transfer';
    reference: string;
    notes: string;
  }>({
    amount: '',
    method: 'cash',
    reference: '',
    notes: ''
  });

  const itemsPerPage = 10;

  // Add the editing payment ID state
  const [editingPaymentId, setEditingPaymentId] = useState<string | null>(null);

  // Helper function to get the latest activity date (payment or booking date)
  const getLatestActivityDate = (booking: Booking): Date => {
    if (!booking.payments || booking.payments.length === 0) {
      return new Date(booking.date);
    }

    // Find the latest payment date
    const latestPaymentDate = booking.payments.reduce((latest, payment) => {
      const paymentDate = new Date(payment.date);
      return paymentDate > latest ? paymentDate : latest;
    }, new Date(0));

    // Return the later of booking date or latest payment date
    const bookingDate = new Date(booking.date);
    return latestPaymentDate > bookingDate ? latestPaymentDate : bookingDate;
  };

  // Filter bookings based on search term and payment status
  const filteredBookings = bookings
    .filter(booking => {
      // Date filter
      if (startDate && endDate) {
        const bookingDate = new Date(booking.date);
        const start = new Date(startDate);
        const end = new Date(endDate);
        end.setHours(23, 59, 59); // Include the entire end date
        if (bookingDate < start || bookingDate > end) return false;
      }

      if (statusFilter === 'all') {
        return true;
      }
      if (['paid', 'partial', 'unpaid'].includes(statusFilter)) {
        const paymentStatus = getPaymentStatus(booking);
        return paymentStatus === statusFilter;
      }
      return booking.status === statusFilter;
    })
    .filter(booking => {
      if (!searchTerm) return true;
      const searchLower = searchTerm.toLowerCase();
      return (
        booking.quoteNumber.toLowerCase().includes(searchLower) ||
        booking.customer.name.toLowerCase().includes(searchLower) ||
        booking.customer.email.toLowerCase().includes(searchLower) ||
        booking.customer.phone.toLowerCase().includes(searchLower)
      );
    })
    // Sort based on selected sort order
    .sort((a, b) => {
      if (sortOrder === 'newest') {
        // Sort by quote number (newest first)
        // Assuming quote numbers are formatted in a way that higher numbers are newer
        return parseInt(b.quoteNumber.replace(/\D/g, '')) - parseInt(a.quoteNumber.replace(/\D/g, ''));
      } else if (sortOrder === 'activity') {
        // Sort by latest activity (payment date or booking date)
        const aLatestActivity = getLatestActivityDate(a);
        const bLatestActivity = getLatestActivityDate(b);
        return bLatestActivity.getTime() - aLatestActivity.getTime();
      }
      return 0;
    });

  // Handle sort order change
  const handleSortOrderChange = (order: SortOrder) => {
    setSortOrder(order);
    setCurrentPage(1); // Reset to first page when changing sort order
  };

  // Calculate total amounts
  const totalBookingAmount = bookings.reduce((sum, booking) => {
    // Only include non-cancelled bookings in total
    return sum + (booking.status === 'cancelled' ? 0 : (booking.totalAmount || 0));
  }, 0);

  const totalPaidAmount = bookings.reduce((sum, booking) => {
    // Sum up all payments for non-cancelled bookings
    if (booking.status !== 'cancelled') {
      return sum + ((booking.payments || []).reduce((pSum, payment) => pSum + payment.amount, 0));
    }
    return sum;
  }, 0);

  const totalPendingAmount = totalBookingAmount - totalPaidAmount;

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'BHD',
      minimumFractionDigits: 3,
      maximumFractionDigits: 3
    }).format(amount);
  };

  // Format currency for CSV export (ensure large numbers are properly formatted)
  const formatCurrencyForCSV = (amount: number) => {
    // Return just the number value without the currency symbol
    // Wrap in quotes to ensure Excel treats it correctly
    return `"${amount.toFixed(3)}"`;
  };

  // Handle CSV export
  const handleExportCSV = () => {
    setIsExporting(true);
    try {
      // Create CSV headers
      const headers = [
        '"Quote Number"',
        '"Date"',
        '"Customer Name"',
        '"Customer Email"',
        '"Customer Phone"',
        '"Customer Address"',
        '"Status"',
        '"Total Amount"',
        '"Paid Amount"',
        '"Remaining Amount"',
        '"Start Date"',
        '"End Date"',
        '"Items"'
      ].join(',');

      // Create CSV rows
      const rows = bookings.map(booking => {
        const totalAmount = booking.status === 'cancelled' ? 0 : (booking.totalAmount || 0);
        const paidAmount = (booking.payments || []).reduce((sum, payment) => sum + payment.amount, 0);
        const remainingAmount = totalAmount - paidAmount;
        const items = booking.products.map(p => `${p.quantity}x ${p.name} (${formatCurrency(p.dailyRate)}/day)`).join('; ');

        // Format amounts with quotes to ensure proper formatting for large numbers
        return [
          `"${booking.quoteNumber}"`,
          `"${new Date(booking.date).toLocaleDateString()}"`,
          `"${booking.customer.name}"`,
          `"${booking.customer.email}"`,
          `"${booking.customer.phone}"`,
          `"${booking.customer.address}"`,
          `"${booking.status}"`,
          formatCurrencyForCSV(totalAmount),
          formatCurrencyForCSV(paidAmount),
          formatCurrencyForCSV(remainingAmount),
          `"${formatDate(booking.rentalPeriod.startDate)}"`,
          `"${formatDate(booking.rentalPeriod.endDate)}"`,
          `"${items}"`
        ].join(',');
      });

      // Combine headers and rows
      const csv = [headers, ...rows].join('\n');

      // Create and download the file
      const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `bookings_${new Date().toISOString().split('T')[0]}.csv`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Error exporting CSV:', error);
    } finally {
      setIsExporting(false);
    }
  };

  // Get paginated bookings
  const paginatedBookings = filteredBookings.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Calculate total pages
  const totalPages = Math.ceil(filteredBookings.length / itemsPerPage);

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    setExpandedBookingId(null);
    window.scrollTo(0, 0);
  };

  // Handle date change
  const handleDateChange = (start: string, end: string) => {
    setStartDate(start);
    setEndDate(end);
    setCurrentPage(1);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Calculate payment status
  const getPaymentStatus = (booking: Booking) => {
    const totalAmount = booking.totalAmount || 0;
    const paidAmount = (booking.payments || []).reduce((sum, payment) => sum + payment.amount, 0);

    if (paidAmount === 0) return 'unpaid';
    if (Math.abs(totalAmount - paidAmount) < 0.001) return 'paid';
    return 'partial';
  };

  // Get payment status badge class
  const getPaymentStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'partial':
        return 'bg-yellow-100 text-yellow-800';
      case 'unpaid':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Calculate payment progress percentage
  const calculatePaymentProgress = (booking: Booking) => {
    const totalAmount = booking.totalAmount || 0;
    if (totalAmount === 0) return 0;

    const paidAmount = (booking.payments || []).reduce((sum, payment) => sum + payment.amount, 0);
    return Math.min(100, Math.round((paidAmount / totalAmount) * 100));
  };

  // Handle adding a payment
  const handleAddPayment = (bookingId: string) => {
    setPaymentBookingId(bookingId);
    setNewPayment({
      amount: '',
      method: 'cash',
      reference: '',
      notes: ''
    });
    setShowPaymentModal(true);
  };

  // Handle editing a payment
  const handleEditPayment = (bookingId: string, paymentId: string) => {
    const booking = bookings.find(b => b.id === bookingId);
    if (!booking || !booking.payments) return;

    const payment = booking.payments.find(p => p.id === paymentId);
    if (!payment) return;

    // Populate the payment form with existing data
    setPaymentBookingId(bookingId);
    setNewPayment({
      amount: payment.amount.toString(),
      method: payment.method as 'cash' | 'card' | 'bank_transfer',
      reference: payment.reference || '',
      notes: payment.notes || ''
    });

    // Set editing state with payment ID
    setEditingPaymentId(paymentId);
    setShowPaymentModal(true);
  };

  // Handle submitting payment
  const handleSubmitPayment = () => {
    if (!paymentBookingId) return;

    const booking = bookings.find(b => b.id === paymentBookingId);
    if (!booking) return;

    // Validate amount
    const amount = parseFloat(newPayment.amount);
    if (isNaN(amount) || amount <= 0) {
      alert('Please enter a valid payment amount');
      return;
    }

    let updatedBooking;

    if (editingPaymentId) {
      // Editing existing payment
      updatedBooking = {
        ...booking,
        payments: (booking.payments || []).map(payment => {
          if (payment.id === editingPaymentId) {
            return {
              ...payment,
              amount: amount,
              method: newPayment.method,
              reference: newPayment.reference || undefined,
              notes: newPayment.notes || undefined
            };
          }
          return payment;
        })
      };
    } else {
      // Adding new payment
      const paymentData: Payment = {
        id: Date.now().toString(),
        booking_id: paymentBookingId,
        amount: amount,
        transaction_id: `TXN-${Date.now()}`,
        date: new Date().toISOString(),
        method: newPayment.method,
        status: 'completed',
        reference: newPayment.reference || undefined,
        notes: newPayment.notes || undefined
      };

      updatedBooking = {
        ...booking,
        payments: [...(booking.payments || []), paymentData]
      };
    }

    // Update the booking in the database
    updateBooking(updatedBooking).then(() => {
      // Update the local state
      const updatedBookings = bookings.map(b =>
        b.id === paymentBookingId ? updatedBooking : b
      );
      onBookingsUpdate(updatedBookings);
      setShowPaymentModal(false);
      setPaymentBookingId(null);
      setEditingPaymentId(null);
    }).catch((error: Error) => {
      console.error('Error updating payment:', error);
      alert(`Failed to update payment: ${error.message}`);
    });
  };

  // Handle deleting a payment
  const handleDeletePayment = (bookingId: string, paymentId: string) => {
    if (!window.confirm('Are you sure you want to delete this payment?')) return;

    const booking = bookings.find(b => b.id === bookingId);
    if (!booking || !booking.payments) return;

    const updatedBooking = {
      ...booking,
      payments: booking.payments.filter(payment => payment.id !== paymentId)
    };

    // Update the booking in the database
    updateBooking(updatedBooking).then(() => {
      // Update the local state
      const updatedBookings = bookings.map(b =>
        b.id === bookingId ? updatedBooking : b
      );
      onBookingsUpdate(updatedBookings);
    }).catch((error: Error) => {
      console.error('Error deleting payment:', error);
    });
  };

  // Handle status change
  const handleStatusChange = (bookingId: string, status: 'pending' | 'confirmed' | 'completed' | 'cancelled') => {
    const booking = bookings.find(b => b.id === bookingId);
    if (!booking) return;

    // Calculate items subtotal using temporary prices if available
    const originalTotal = booking.products.reduce((sum, product) => {
      const productDays = product.customDays || booking.rentalPeriod.days;
      const dailyRate = product.temporaryDailyRate || product.dailyRate;
      const weeklyRate = product.temporaryWeeklyRate || product.weeklyRate;

      if (booking.rentalPeriod.rentalType === 'weekly' && weeklyRate) {
        const weeks = Math.ceil(productDays / 7);
        return sum + (weeklyRate * weeks * (product.quantity || 1));
      } else {
        return sum + (dailyRate * productDays * (product.quantity || 1));
      }
    }, 0);

    // Add delivery fee if applicable
    const deliveryFee = booking.delivery?.fee || 0;
    const subtotal = originalTotal;
    const subtotalWithDelivery = subtotal + deliveryFee;

    // Calculate discount
    const discount = booking.coupon ?
      (booking.coupon.discountType === 'percentage' ?
        (subtotalWithDelivery * booking.coupon.discountValue / 100) :
        booking.coupon.discountValue) : 0;

    // Calculate taxable amount and tax
    const taxableAmount = subtotalWithDelivery - discount;
    const tax = taxableAmount * (systemSettings.enableTax ? systemSettings.taxRate / 100 : 0);

    // Set final total amount based on status
    const totalAmount = status === 'cancelled' ? 0 : (taxableAmount + tax);

    const updatedBooking = {
      ...booking,
      status,
      totalAmount,
      subtotal,
      deliveryFee,
      discount,
      tax,
      total: totalAmount,
      // Ensure all required fields are present for PHP API
      id: booking.id,
      quoteNumber: booking.quoteNumber || booking.id,
      date: booking.date,
      customer: booking.customer,
      products: booking.products,
      rentalPeriod: booking.rentalPeriod,
      // Preserve the coupon property to ensure discount is maintained
      coupon: booking.coupon
    };

    // Update the booking in the database
    updateBooking(updatedBooking).then(() => {
      // Update the local state
      const updatedBookings = bookings.map(b =>
        b.id === bookingId ? updatedBooking : b
      );
      onBookingsUpdate(updatedBookings);
    }).catch((error: Error) => {
      console.error('Error updating booking status:', error);
    });
  };

  return (
    <div>
      <div className="bg-white rounded-lg shadow border border-gray-200 p-3 sm:p-4 mb-4">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-3 gap-2">
          <div className="flex items-center gap-2 flex-wrap">
            <h2 className="text-xl font-bold mr-2">Booking Management</h2>
            <button
              onClick={handleExportCSV}
              disabled={isExporting}
              className={`px-3 py-1.5 bg-green-600 text-white rounded-md flex items-center text-sm ${
                isExporting ? 'opacity-50 cursor-not-allowed' : 'hover:bg-green-700'
              }`}
            >
              <Download size={16} className="mr-1" />
              {isExporting ? 'Exporting...' : 'Export CSV'}
            </button>

            {/* Sort Order Selector */}
            <div className="flex items-center">
              <span className="mr-1 text-xs text-gray-600">Sort by:</span>
              <div className="relative inline-block">
                <select
                  value={sortOrder}
                  onChange={(e) => handleSortOrderChange(e.target.value as SortOrder)}
                  className="pl-7 pr-3 py-1.5 border border-gray-300 rounded-md appearance-none bg-white text-sm"
                >
                  <option value="newest">Newest Quote Number</option>
                  <option value="activity">Latest Activity</option>
                </select>
                <div className="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                  <SortDesc size={14} className="text-gray-500" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Compact Stats Dashboard */}
        <div className="mb-3">
          <BookingStats
            bookings={bookings}
            getPaymentStatus={getPaymentStatus}
            totalBookingAmount={totalBookingAmount}
            totalPaidAmount={totalPaidAmount}
            totalPendingAmount={totalPendingAmount}
            formatCurrency={formatCurrency}
          />
        </div>

        {/* Filters section */}
        <div className="mt-3">
          <BookingFilters
            searchTerm={searchTerm}
            statusFilter={statusFilter}
            startDate={startDate}
            endDate={endDate}
            onSearchChange={setSearchTerm}
            onStatusFilterChange={setStatusFilter}
            onDateChange={handleDateChange}
          />
        </div>
      </div>

      {/* Bookings List */}
      <div className="w-full overflow-x-auto">
        <BookingList
          bookings={paginatedBookings}
          products={products}
          systemSettings={systemSettings}
          onBookingsUpdate={onBookingsUpdate}
          expandedBookingId={expandedBookingId}
          setExpandedBookingId={setExpandedBookingId}
          onDeleteBooking={() => {}}
          onAddPayment={handleAddPayment}
          onEditPayment={handleEditPayment}
          onDeletePayment={handleDeletePayment}
          formatDate={formatDate}
          getPaymentStatus={getPaymentStatus}
          getPaymentStatusBadgeClass={getPaymentStatusBadgeClass}
          calculatePaymentProgress={calculatePaymentProgress}
          onStatusChange={handleStatusChange}
        />
      </div>

      {filteredBookings.length === 0 && (
        <div className="text-center py-8 bg-gray-50 rounded-md mt-4">
          <p className="text-gray-500">No bookings found matching your search criteria.</p>
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center items-center mt-6 space-x-2">
          <button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className={`p-2 rounded-md ${
              currentPage === 1
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-300'
            }`}
          >
            <ChevronLeft size={20} />
          </button>

          <div className="flex items-center space-x-1">
            {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
              <button
                key={page}
                onClick={() => handlePageChange(page)}
                className={`px-3 py-1 rounded-md ${
                  currentPage === page
                    ? 'bg-blue-600 text-white'
                    : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-300'
                }`}
              >
                {page}
              </button>
            ))}
          </div>

          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className={`p-2 rounded-md ${
              currentPage === totalPages
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-300'
            }`}
          >
            <ChevronRight size={20} />
          </button>
        </div>
      )}

      {/* Payment Modal */}
      {showPaymentModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-lg w-full max-w-md">
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
              <h2 className="text-xl font-bold">{editingPaymentId ? 'Edit Payment' : 'Add Payment'}</h2>
              <button
                onClick={() => {
                  setShowPaymentModal(false);
                  setEditingPaymentId(null);
                }}
                className="text-gray-400 hover:text-gray-500"
              >
                <X size={24} />
              </button>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {/* Amount */}
                <div>
                  <label className="block text-gray-700 mb-2 font-medium">
                    Amount*
                  </label>
                  <div className="relative">
                    <span className="absolute inset-y-0 left-0 pl-3 flex items-center text-gray-500">
                      BHD
                    </span>
                    <input
                      type="number"
                      value={newPayment.amount}
                      onChange={(e) => setNewPayment({...newPayment, amount: e.target.value})}
                      className="w-full pl-12 pr-4 py-2 border border-gray-300 rounded-md"
                      placeholder="0.000"
                      min="0.001"
                      step="0.001"
                    />
                  </div>
                </div>

                {/* Payment Method */}
                <div>
                  <label className="block text-gray-700 mb-2 font-medium">
                    Payment Method
                  </label>
                  <div className="grid grid-cols-3 gap-2">
                    <button
                      onClick={() => setNewPayment({...newPayment, method: 'cash'})}
                      className={`flex items-center justify-center p-3 rounded-md ${
                        newPayment.method === 'cash'
                          ? 'bg-green-100 text-green-800 ring-1 ring-green-300'
                          : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                      }`}
                    >
                      <DollarSign size={18} className="mr-1" />
                      Cash
                    </button>
                    <button
                      onClick={() => setNewPayment({...newPayment, method: 'card'})}
                      className={`flex items-center justify-center p-3 rounded-md ${
                        newPayment.method === 'card'
                          ? 'bg-blue-100 text-blue-800 ring-1 ring-blue-300'
                          : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                      }`}
                    >
                      <CreditCard size={18} className="mr-1" />
                      Card
                    </button>
                    <button
                      onClick={() => setNewPayment({...newPayment, method: 'bank_transfer'})}
                      className={`flex items-center justify-center p-3 rounded-md ${
                        newPayment.method === 'bank_transfer'
                          ? 'bg-purple-100 text-purple-800 ring-1 ring-purple-300'
                          : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                      }`}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1">
                        <rect x="2" y="5" width="20" height="14" rx="2" />
                        <line x1="2" y1="10" x2="22" y2="10" />
                      </svg>
                      Bank
                    </button>
                  </div>
                </div>

                {/* Reference */}
                <div>
                  <label className="block text-gray-700 mb-2 font-medium">
                    Reference / Transaction ID
                  </label>
                  <input
                    type="text"
                    value={newPayment.reference}
                    onChange={(e) => setNewPayment({...newPayment, reference: e.target.value})}
                    className="w-full p-2 border border-gray-300 rounded-md"
                    placeholder="Enter reference or transaction ID"
                  />
                </div>

                {/* Notes */}
                <div>
                  <label className="block text-gray-700 mb-2 font-medium">
                    Notes
                  </label>
                  <textarea
                    value={newPayment.notes}
                    onChange={(e) => setNewPayment({...newPayment, notes: e.target.value})}
                    className="w-full p-2 border border-gray-300 rounded-md"
                    rows={3}
                    placeholder="Add any additional notes"
                  />
                </div>
              </div>

              <div className="flex justify-end mt-6 space-x-2">
                <button
                  onClick={() => {
                    setShowPaymentModal(false);
                    setEditingPaymentId(null);
                  }}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700"
                >
                  Cancel
                </button>
                <button
                  onClick={handleSubmitPayment}
                  disabled={!newPayment.amount || parseFloat(newPayment.amount) <= 0}
                  className={`px-4 py-2 rounded-md ${
                    newPayment.amount && parseFloat(newPayment.amount) > 0
                      ? 'bg-green-600 text-white hover:bg-green-700'
                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  }`}
                >
                  {editingPaymentId ? 'Update Payment' : 'Add Payment'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BookingManagement;