<?php
require_once 'config/database.php';

session_start();

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGet($database);
            break;
        case 'POST':
            handlePost($database);
            break;
        case 'PUT':
            handlePut($database);
            break;
        case 'DELETE':
            handleDelete($database);
            break;
        default:
            sendError('Method not allowed', 405);
    }
    
} catch (Exception $e) {
    error_log("Bookings API error: " . $e->getMessage());
    sendError('Internal server error', 500);
}

function handleGet($database) {
    $query = "
        SELECT b.*,
               GROUP_CONCAT(
                 CONCAT(bp.product_id, ':', bp.quantity, ':', bp.daily_rate, ':', bp.weekly_rate, ':', bp.total_price)
                 SEPARATOR '|'
               ) as products_data
        FROM bookings b
        LEFT JOIN booking_products bp ON b.id = bp.booking_id
        GROUP BY b.id
        ORDER BY b.date DESC
    ";

    $bookings = $database->getMany($query);

    // Format bookings to match frontend expectations
    $formattedBookings = array_map(function($booking) use ($database) {
        $products = [];
        if ($booking['products_data']) {
            $productDataArray = explode('|', $booking['products_data']);
            foreach ($productDataArray as $productData) {
                $parts = explode(':', $productData);
                if (count($parts) >= 5) {
                    $productId = $parts[0];

                    // Fetch full product details from products table
                    $productQuery = "SELECT * FROM products WHERE id = ?";
                    $fullProduct = $database->getOne($productQuery, [$productId]);

                    if ($fullProduct) {
                        $products[] = [
                            'id' => $parts[0],
                            'name' => $fullProduct['name'],
                            'category' => $fullProduct['category'],
                            'sku' => $fullProduct['sku'],
                            'description' => $fullProduct['description'] ?? '',
                            'image' => $fullProduct['image'] ?? 'https://placehold.co/400x300/2563eb/ffffff?text=Product',
                            'available' => (bool)$fullProduct['available'],
                            'stock' => (int)$fullProduct['stock'],
                            'quantity' => (int)$parts[1],
                            'dailyRate' => (float)$parts[2],
                            'weeklyRate' => $parts[3] !== 'null' ? (float)$parts[3] : null,
                            'totalPrice' => (float)$parts[4]
                        ];
                    } else {
                        // Fallback if product not found in products table
                        $products[] = [
                            'id' => $parts[0],
                            'name' => 'Unknown Product',
                            'category' => 'Unknown',
                            'sku' => '',
                            'description' => '',
                            'image' => 'https://placehold.co/400x300/2563eb/ffffff?text=Product',
                            'available' => true,
                            'stock' => 0,
                            'quantity' => (int)$parts[1],
                            'dailyRate' => (float)$parts[2],
                            'weeklyRate' => $parts[3] !== 'null' ? (float)$parts[3] : null,
                            'totalPrice' => (float)$parts[4]
                        ];
                    }
                }
            }
        }
        
        // Get payments for this booking
        $paymentsQuery = "SELECT * FROM payments WHERE booking_id = ? ORDER BY date DESC";
        $payments = $database->getMany($paymentsQuery, [$booking['id']]);

        // Format payments
        $formattedPayments = array_map(function($payment) {
            return [
                'id' => $payment['id'],
                'amount' => (float)$payment['amount'],
                'method' => $payment['method'],
                'reference' => $payment['reference'],
                'notes' => $payment['notes'],
                'date' => $payment['date'],
                'status' => $payment['status']
            ];
        }, $payments);

        // If no payments exist but paid_amount > 0 AND remaining_amount < total, create a synthetic payment record
        // This ensures we only create synthetic payments for actually paid bookings, not new unpaid ones
        if (empty($formattedPayments) && $booking['paid_amount'] > 0 && $booking['remaining_amount'] < $booking['total']) {
            $formattedPayments = [[
                'id' => 'migrated_' . $booking['id'],
                'amount' => (float)$booking['paid_amount'],
                'method' => 'cash', // Default method for migrated payments
                'reference' => 'Migrated from Firebase',
                'notes' => 'Payment migrated from Firebase database',
                'date' => $booking['date'],
                'status' => 'completed'
            ]];
        }

        return [
            'id' => $booking['id'],
            'quoteNumber' => $booking['quote_number'],
            'date' => $booking['date'],
            'customer' => [
                'name' => $booking['customer_name'],
                'email' => $booking['customer_email'],
                'phone' => $booking['customer_phone'],
                'address' => $booking['customer_address']
            ],
            'products' => $products,
            'rentalPeriod' => [
                'dates' => json_decode($booking['rental_dates'], true),
                'days' => (int)$booking['rental_days'],
                'rentalType' => $booking['rental_type']
            ],
            'status' => $booking['status'],
            'subtotal' => (float)$booking['subtotal'],
            'deliveryFee' => (float)$booking['delivery_fee'],
            'discount' => (float)$booking['discount'],
            'tax' => (float)$booking['tax'],
            'total' => (float)$booking['total'],
            'totalAmount' => (float)$booking['total'], // Frontend expects this field
            'paidAmount' => $booking['paid_amount'] ? (float)$booking['paid_amount'] : 0,
            'remainingAmount' => $booking['remaining_amount'] ? (float)$booking['remaining_amount'] : 0,
            'payments' => $formattedPayments // Frontend expects this array
        ];
    }, $bookings);
    
    sendResponse($formattedBookings);
}

function handlePost($database) {
    $data = getRequestData();

    validateRequired($data, ['date', 'customer', 'products', 'rentalPeriod', 'total']);

    // Generate quote number server-side to avoid race conditions
    $quoteNumber = generateQuoteNumber($database);
    $id = $quoteNumber;
    
    // Insert booking
    $bookingQuery = "INSERT INTO bookings (
        id, quote_number, date, customer_name, customer_email, customer_phone, customer_address,
        rental_dates, rental_days, rental_type, status, subtotal, delivery_fee, discount,
        tax, total, paid_amount, remaining_amount, delivery_option_id, coupon_id
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    // Convert ISO date to MySQL format
    $mysqlDate = date('Y-m-d H:i:s', strtotime($data['date']));

    $bookingParams = [
        $id,
        $quoteNumber, // Use the generated quote number
        $mysqlDate,
        sanitizeInput($data['customer']['name']),
        sanitizeInput($data['customer']['email']),
        sanitizeInput($data['customer']['phone'] ?? null),
        sanitizeInput($data['customer']['address'] ?? null),
        json_encode($data['rentalPeriod']['dates']),
        $data['rentalPeriod']['days'],
        sanitizeInput($data['rentalPeriod']['rentalType']),
        sanitizeInput($data['status'] ?? 'pending'),
        $data['subtotal'] ?? 0,
        $data['deliveryFee'] ?? 0,
        $data['discount'] ?? 0,
        $data['tax'] ?? 0,
        $data['total'],
        $data['paidAmount'] ?? 0,
        $data['remainingAmount'] ?? $data['total'],
        sanitizeInput($data['delivery']['option']['id'] ?? null),
        sanitizeInput($data['coupon']['id'] ?? null)
    ];
    
    $database->executeQuery($bookingQuery, $bookingParams);
    
    // Insert booking products
    foreach ($data['products'] as $product) {
        // Calculate totalPrice if not provided
        $totalPrice = $product['totalPrice'] ?? ($product['dailyRate'] * $product['quantity']);

        $productQuery = "INSERT INTO booking_products (booking_id, product_id, quantity, daily_rate, weekly_rate, total_price)
                        VALUES (?, ?, ?, ?, ?, ?)";
        $productParams = [
            $id,
            sanitizeInput($product['id']),
            $product['quantity'],
            $product['dailyRate'],
            $product['weeklyRate'] ?? null,
            $totalPrice
        ];
        $database->executeQuery($productQuery, $productParams);
    }
    
    sendResponse(['id' => $id]);
}

function generateQuoteNumber($database) {
    // Simple approach without transactions for now
    // Get current settings
    $query = "SELECT last_quote_number FROM system_settings WHERE id = 'general'";
    $result = $database->getOne($query);

    $lastNumber = $result ? (int)$result['last_quote_number'] : 1000;
    $nextNumber = $lastNumber + 1;

    // Update the last quote number
    $updateQuery = "UPDATE system_settings SET last_quote_number = ? WHERE id = 'general'";
    $database->executeQuery($updateQuery, [$nextNumber]);

    // Format quote number with leading zeros
    return 'Q-' . str_pad($nextNumber, 6, '0', STR_PAD_LEFT);
}

function handlePut($database) {
    $data = getRequestData();

    validateRequired($data, ['id', 'quoteNumber', 'date', 'customer', 'products', 'rentalPeriod', 'total']);

    // Debug logging
    error_log("PUT request for booking ID: " . $data['id']);

    // Check if booking exists
    $checkQuery = "SELECT id FROM bookings WHERE id = ?";
    $existingBooking = $database->getOne($checkQuery, [$data['id']]);

    if (!$existingBooking) {
        error_log("Booking not found: " . $data['id']);
        sendError('Booking not found', 404);
        return;
    }

    error_log("Booking found, proceeding with update: " . $data['id']);

    // Update booking
    $bookingQuery = "UPDATE bookings SET
        quote_number = ?, date = ?, customer_name = ?, customer_email = ?, customer_phone = ?,
        customer_address = ?, rental_dates = ?, rental_days = ?, rental_type = ?, status = ?,
        subtotal = ?, delivery_fee = ?, discount = ?, tax = ?, total = ?, paid_amount = ?,
        remaining_amount = ?, delivery_option_id = ?, coupon_id = ?
    WHERE id = ?";
    
    // Convert ISO date to MySQL format
    $mysqlDate = date('Y-m-d H:i:s', strtotime($data['date']));

    $bookingParams = [
        sanitizeInput($data['quoteNumber']),
        $mysqlDate,
        sanitizeInput($data['customer']['name']),
        sanitizeInput($data['customer']['email']),
        sanitizeInput($data['customer']['phone'] ?? null),
        sanitizeInput($data['customer']['address'] ?? null),
        json_encode($data['rentalPeriod']['dates']),
        $data['rentalPeriod']['days'],
        sanitizeInput($data['rentalPeriod']['rentalType']),
        sanitizeInput($data['status'] ?? 'pending'),
        $data['subtotal'] ?? 0,
        $data['deliveryFee'] ?? 0,
        $data['discount'] ?? 0,
        $data['tax'] ?? 0,
        $data['total'],
        $data['paidAmount'] ?? 0,
        $data['remainingAmount'] ?? 0,
        sanitizeInput($data['delivery']['option']['id'] ?? null),
        sanitizeInput($data['coupon']['id'] ?? null),
        sanitizeInput($data['id'])
    ];
    
    $rowsAffected = $database->update($bookingQuery, $bookingParams);
    
    if ($rowsAffected === 0) {
        sendError('Booking not found', 404);
    }
    
    // Delete existing booking products
    $database->delete("DELETE FROM booking_products WHERE booking_id = ?", [$data['id']]);
    
    // Insert updated booking products
    foreach ($data['products'] as $product) {
        // Calculate totalPrice if not provided
        $totalPrice = $product['totalPrice'] ?? ($product['dailyRate'] * $product['quantity']);

        $productQuery = "INSERT INTO booking_products (booking_id, product_id, quantity, daily_rate, weekly_rate, total_price)
                        VALUES (?, ?, ?, ?, ?, ?)";
        $productParams = [
            sanitizeInput($data['id']),
            sanitizeInput($product['id']),
            $product['quantity'],
            $product['dailyRate'],
            $product['weeklyRate'] ?? null,
            $totalPrice
        ];
        $database->executeQuery($productQuery, $productParams);
    }
    
    sendSuccess('Booking updated successfully');
}

function handleDelete($database) {
    $id = $_GET['id'] ?? null;
    
    if (!$id) {
        sendError('Booking ID is required');
    }
    
    // Booking products will be deleted automatically due to CASCADE
    $query = "DELETE FROM bookings WHERE id = ?";
    $rowsAffected = $database->delete($query, [sanitizeInput($id)]);
    
    if ($rowsAffected === 0) {
        sendError('Booking not found', 404);
    }
    
    sendSuccess('Booking deleted successfully');
}
?>
