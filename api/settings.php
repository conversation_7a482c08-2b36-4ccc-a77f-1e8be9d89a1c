<?php
require_once 'config/database.php';

session_start();

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGet($database);
            break;
        case 'POST':
        case 'PUT':
            handleUpdate($database);
            break;
        default:
            sendError('Method not allowed', 405);
    }
    
} catch (Exception $e) {
    error_log("Settings API error: " . $e->getMessage());
    sendError('Internal server error', 500);
}

function handleGet($database) {
    // Get all settings from system_settings table
    $query = "SELECT setting_key, setting_value FROM system_settings";
    $settingsRows = $database->getMany($query);

    // Convert to key-value array
    $settings = [];
    foreach ($settingsRows as $row) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }

    // Get the highest quote number from bookings
    $quoteQuery = "SELECT MAX(CAST(SUBSTRING(quote_number, 3) AS UNSIGNED)) as max_quote FROM bookings WHERE quote_number LIKE 'Q-%'";
    $quoteResult = $database->getOne($quoteQuery);
    $lastQuoteNumber = $quoteResult ? (int)$quoteResult['max_quote'] : 1000;

    $response = [
        'taxRate' => (float)($settings['tax_rate'] ?? 0),
        'enableTax' => (bool)($settings['tax_rate'] ?? 0 > 0),
        'deliveryOptions' => [],
        'lastQuoteNumber' => $lastQuoteNumber
    ];

    sendResponse($response);
    return;
    
    $settingsData = $settings['settings_data'] ? json_decode($settings['settings_data'], true) : [];
    
    $response = [
        'taxRate' => (float)$settings['tax_rate'],
        'enableTax' => (bool)$settings['enable_tax'],
        'lastQuoteNumber' => (int)$settings['last_quote_number'],
        'deliveryOptions' => $settingsData['deliveryOptions'] ?? []
    ];
    
    sendResponse($response);
}

function handleUpdate($database) {
    $data = getRequestData();
    
    $query = "INSERT INTO system_settings (id, tax_rate, enable_tax, last_quote_number, settings_data)
              VALUES (?, ?, ?, ?, ?)
              ON DUPLICATE KEY UPDATE
              tax_rate = VALUES(tax_rate),
              enable_tax = VALUES(enable_tax),
              last_quote_number = VALUES(last_quote_number),
              settings_data = VALUES(settings_data)";
    
    $params = [
        'general',
        $data['taxRate'] ?? 0,
        $data['enableTax'] ?? false,
        $data['lastQuoteNumber'] ?? 0,
        json_encode($data)
    ];
    
    $database->executeQuery($query, $params);
    
    sendSuccess('Settings updated successfully');
}
?>
