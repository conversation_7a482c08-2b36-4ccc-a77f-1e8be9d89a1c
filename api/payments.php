<?php
require_once 'config/database.php';

session_start();

try {
    $database = new Database();
    $db = $database->getConnection();
    
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            handleGet($database);
            break;
        case 'POST':
            handlePost($database);
            break;
        case 'PUT':
            handlePut($database);
            break;
        case 'DELETE':
            handleDelete($database);
            break;
        default:
            sendError('Method not allowed', 405);
    }
    
} catch (Exception $e) {
    error_log("Payments API error: " . $e->getMessage());
    sendError('Internal server error', 500);
}

function handleGet($database) {
    $bookingId = $_GET['booking_id'] ?? null;
    
    if ($bookingId) {
        // Get payments for a specific booking
        $query = "SELECT * FROM payments WHERE booking_id = ? ORDER BY date DESC";
        $payments = $database->getMany($query, [$bookingId]);
        sendResponse($payments);
    } else {
        // Get all payments
        $query = "SELECT p.*, b.quote_number, b.customer_name 
                  FROM payments p 
                  LEFT JOIN bookings b ON p.booking_id = b.id 
                  ORDER BY p.date DESC";
        $payments = $database->getMany($query);
        sendResponse($payments);
    }
}

function handlePost($database) {
    $data = getRequestData();
    
    validateRequired($data, ['booking_id', 'amount', 'method', 'date']);
    
    // Generate payment ID
    $paymentId = 'PAY-' . date('YmdHis') . '-' . substr(md5(uniqid()), 0, 6);
    
    // Insert payment
    $paymentQuery = "INSERT INTO payments (
        id, booking_id, amount, transaction_id, date, method, reference, notes, status
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $paymentParams = [
        $paymentId,
        sanitizeInput($data['booking_id']),
        $data['amount'],
        sanitizeInput($data['transaction_id'] ?? null),
        $data['date'],
        sanitizeInput($data['method']),
        sanitizeInput($data['reference'] ?? null),
        sanitizeInput($data['notes'] ?? null),
        sanitizeInput($data['status'] ?? 'completed')
    ];
    
    $database->executeQuery($paymentQuery, $paymentParams);
    
    // Update booking paid_amount and remaining_amount
    updateBookingPaymentTotals($database, $data['booking_id']);
    
    sendResponse(['id' => $paymentId]);
}

function handlePut($database) {
    $data = getRequestData();
    
    validateRequired($data, ['id', 'booking_id', 'amount', 'method', 'date']);
    
    // Update payment
    $paymentQuery = "UPDATE payments SET 
        amount = ?, transaction_id = ?, date = ?, method = ?, reference = ?, notes = ?, status = ?
        WHERE id = ?";
    
    $paymentParams = [
        $data['amount'],
        sanitizeInput($data['transaction_id'] ?? null),
        $data['date'],
        sanitizeInput($data['method']),
        sanitizeInput($data['reference'] ?? null),
        sanitizeInput($data['notes'] ?? null),
        sanitizeInput($data['status'] ?? 'completed'),
        sanitizeInput($data['id'])
    ];
    
    $rowsAffected = $database->update($paymentQuery, $paymentParams);
    
    if ($rowsAffected === 0) {
        sendError('Payment not found', 404);
    }
    
    // Update booking paid_amount and remaining_amount
    updateBookingPaymentTotals($database, $data['booking_id']);
    
    sendSuccess('Payment updated successfully');
}

function handleDelete($database) {
    $id = $_GET['id'] ?? null;
    $bookingId = $_GET['booking_id'] ?? null;
    
    if (!$id || !$bookingId) {
        sendError('Payment ID and booking ID are required');
    }
    
    $query = "DELETE FROM payments WHERE id = ?";
    $rowsAffected = $database->delete($query, [sanitizeInput($id)]);
    
    if ($rowsAffected === 0) {
        sendError('Payment not found', 404);
    }
    
    // Update booking paid_amount and remaining_amount
    updateBookingPaymentTotals($database, $bookingId);
    
    sendSuccess('Payment deleted successfully');
}

function updateBookingPaymentTotals($database, $bookingId) {
    // Calculate total paid amount for this booking
    $query = "SELECT COALESCE(SUM(amount), 0) as total_paid FROM payments WHERE booking_id = ? AND status = 'completed'";
    $result = $database->getOne($query, [$bookingId]);
    $totalPaid = $result['total_paid'] ?? 0;
    
    // Get booking total
    $bookingQuery = "SELECT total FROM bookings WHERE id = ?";
    $booking = $database->getOne($bookingQuery, [$bookingId]);
    
    if (!$booking) {
        throw new Exception("Booking not found: " . $bookingId);
    }
    
    $total = $booking['total'];
    $remainingAmount = max(0, $total - $totalPaid);
    
    // Update booking
    $updateQuery = "UPDATE bookings SET paid_amount = ?, remaining_amount = ? WHERE id = ?";
    $database->update($updateQuery, [$totalPaid, $remainingAmount, $bookingId]);
}
?>
